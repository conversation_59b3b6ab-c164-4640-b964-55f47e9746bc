## Summary
Describe what you changed and why.

## Links
- References: #1 / #2 (do **not** use Fixes/Closes to avoid auto-closing shared issues)

## Checklist (must pass CI)
- [ ] Branch name starts with **my GitHub username** (e.g., `username/feature-...`)
- [ ] I added my **real name + @username** to README under **Contributors**
- [ ] I created `students/<lastname>-bio.md` and included **@username**
- [ ] At least one commit uses **Conventional Commits** (feat/fix/docs/...) **and** references an issue `(#1)` or `(#2)`
